import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

interface MoonAnimationProps {
  isNightMode: boolean; // true quand mode = 'night'
  currentMode: string;  // pour détecter les changements de mode
}

const MoonAnimation: React.FC<MoonAnimationProps> = ({ isNightMode, currentMode }) => {
  const moonRef = useRef<HTMLDivElement>(null);
  const haloRef = useRef<HTMLDivElement>(null); // 🔧 CISCO: Référence séparée pour le halo
  const animationRef = useRef<gsap.core.Timeline | null>(null);
  const fadeOutRef = useRef<gsap.core.Tween | null>(null);

  useEffect(() => {
    if (!moonRef.current || !haloRef.current) return;

    // 🌙 CISCO: Mode Nuit profonde - Apparition et descente de la lune
    if (isNightMode && currentMode === 'night') {
      // Arrêter toute animation en cours
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }

      // 🔧 CISCO: Position initiale de la lune - haut gauche de l'écran
      gsap.set(moonRef.current, {
        x: '15vw', // Plus à gauche pour commencer la courbe
        y: '10vh', // Plus haut pour la courbe parabolique
        xPercent: -50,
        yPercent: -50,
        opacity: 0,
        scale: 1,
        display: 'block'
      });

      // Position initiale du halo (même position que la lune)
      gsap.set(haloRef.current, {
        x: '15vw',
        y: '10vh',
        xPercent: -50,
        yPercent: -50,
        opacity: 0,
        display: 'block'
      });

      // 🔧 CISCO: Timeline simple sans délai initial
      animationRef.current = gsap.timeline();

      // 🌙 CISCO: Phase 1: Apparition douce de la lune et du halo
      animationRef.current.to(moonRef.current, {
        opacity: 1.0,
        duration: 3,
        ease: "power2.out"
      });

      // Apparition du halo en parallèle
      animationRef.current.to(haloRef.current, {
        opacity: 0.25,
        duration: 3,
        ease: "power2.out"
      }, 0);

      // 🔧 CISCO: Phase 2: Trajectoire diagonale PARFAITE - Courbe parabolique mathématique
      // ÉLIMINATION TOTALE du mouvement en "L" - Diagonale pure et fluide
      // Durée : 480 secondes (8 minutes) pour mouvement astronomique réaliste

      // 🌙 CISCO: Animation lune - Courbe parabolique mathématiquement parfaite
      animationRef.current.to(moonRef.current, {
        keyframes: [
          // Courbe parabolique calculée : y = ax² + bx + c
          { x: '10vw', y: '8vh', duration: 0 },     // Point départ haut-gauche
          { x: '20vw', y: '6vh', duration: 0.1 },   // Montée initiale douce
          { x: '30vw', y: '5vh', duration: 0.2 },   // Sommet de la parabole
          { x: '40vw', y: '6vh', duration: 0.3 },   // Début descente symétrique
          { x: '50vw', y: '12vh', duration: 0.4 },  // Descente progressive
          { x: '60vw', y: '22vh', duration: 0.5 },  // Accélération descente
          { x: '70vw', y: '35vh', duration: 0.6 },  // Descente continue
          { x: '80vw', y: '50vh', duration: 0.75 }, // Descente rapide
          { x: '90vw', y: '68vh', duration: 0.9 },  // Approche finale
          { x: '95vw', y: '75vh', duration: 1.0 }   // Point final bas-droite
        ],
        duration: 480, // 🔧 CISCO: 8 minutes - mouvement astronomique lent
        ease: "power1.inOut", // Easing naturel pour parabole fluide
        transformOrigin: "center center"
      }, 3); // Commence après apparition

      // 🌙 CISCO: Halo synchronisé avec trajectoire identique
      animationRef.current.to(haloRef.current, {
        keyframes: [
          { x: '10vw', y: '8vh', duration: 0 },
          { x: '20vw', y: '6vh', duration: 0.1 },
          { x: '30vw', y: '5vh', duration: 0.2 },
          { x: '40vw', y: '6vh', duration: 0.3 },
          { x: '50vw', y: '12vh', duration: 0.4 },
          { x: '60vw', y: '22vh', duration: 0.5 },
          { x: '70vw', y: '35vh', duration: 0.6 },
          { x: '80vw', y: '50vh', duration: 0.75 },
          { x: '90vw', y: '68vh', duration: 0.9 },
          { x: '95vw', y: '75vh', duration: 1.0 }
        ],
        duration: 480, // Synchronisé parfaitement
        ease: "power1.inOut"
      }, 3);

    } else if (!isNightMode && currentMode !== 'night') {
      // Arrêter l'animation de descente
      if (animationRef.current) {
        animationRef.current.kill();
        animationRef.current = null;
      }

      // Si la lune est visible, la faire disparaître en douceur avec le halo
      if (moonRef.current && gsap.getProperty(moonRef.current, "opacity") > 0) {
        fadeOutRef.current = gsap.timeline();

        // Disparition de la lune
        fadeOutRef.current.to(moonRef.current, {
          opacity: 0,
          duration: 8,
          ease: "power2.in"
        });

        // Disparition du halo en parallèle
        fadeOutRef.current.to(haloRef.current, {
          opacity: 0,
          duration: 8,
          ease: "power2.in",
          onComplete: () => {
            if (moonRef.current && haloRef.current) {
              gsap.set(moonRef.current, { display: 'none' });
              gsap.set(haloRef.current, { display: 'none' });
            }
          }
        }, 0); // En même temps que la lune
      } else {
        // Si déjà invisible, juste les cacher
        gsap.set(moonRef.current, { display: 'none' });
        gsap.set(haloRef.current, { display: 'none' });
      }
    }

    // Nettoyage au démontage
    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }
    };
  }, [isNightMode, currentMode]);

  return (
    <>
      {/* 🌙 CISCO: Halo lumineux séparé pour éviter l'effet carré */}
      <div
        ref={haloRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 8, // 🔧 CISCO: Lune + Halo derrière les nuages (z-index 8)
          display: 'none',
          width: '200px',
          height: '200px',
          background: 'radial-gradient(circle, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 30%, rgba(255, 255, 255, 0.04) 60%, transparent 100%)', // 🔧 CISCO: Halo plus lumineux
          borderRadius: '50%',
          transform: 'translate(-50%, -50%)',
        }}
      />

      {/* 🌙 CISCO: Lune principale */}
      <div
        ref={moonRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 8, // 🔧 CISCO: Lune + Halo derrière les nuages (z-index 8)
          display: 'none',
          width: '120px',
          height: '120px',
          backgroundImage: 'url(/Lune-Moon.png)',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          filter: 'brightness(1.6) contrast(1.3)', // 🔧 CISCO: Plus lumineuse (était 1.3/1.1)
        }}
        title="🌙 Lune nocturne"
      />
    </>
  );
};

export default MoonAnimation;
