import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

interface MoonAnimationProps {
  isNightMode: boolean; // true quand mode = 'night'
  currentMode: string;  // pour détecter les changements de mode
}

const MoonAnimation: React.FC<MoonAnimationProps> = ({ isNightMode, currentMode }) => {
  const moonRef = useRef<HTMLDivElement>(null);
  const haloRef = useRef<HTMLDivElement>(null); // 🔧 CISCO: Référence séparée pour le halo
  const animationRef = useRef<gsap.core.Timeline | null>(null);
  const fadeOutRef = useRef<gsap.core.Tween | null>(null);

  useEffect(() => {
    if (!moonRef.current || !haloRef.current) return;

    // 🌙 CISCO: Mode Nuit profonde - Apparition et descente de la lune
    if (isNightMode && currentMode === 'night') {
      // Arrêter toute animation en cours
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }

      // 🔧 CISCO: Position initiale de la lune - haut gauche de l'écran
      gsap.set(moonRef.current, {
        x: '15vw', // Plus à gauche pour commencer la courbe
        y: '10vh', // Plus haut pour la courbe parabolique
        xPercent: -50,
        yPercent: -50,
        opacity: 0,
        scale: 1,
        display: 'block'
      });

      // Position initiale du halo (même position que la lune)
      gsap.set(haloRef.current, {
        x: '15vw',
        y: '10vh',
        xPercent: -50,
        yPercent: -50,
        opacity: 0,
        display: 'block'
      });

      // 🔧 CISCO: Créer la timeline avec DÉLAI INITIAL de 10 secondes
      animationRef.current = gsap.timeline({
        delay: 10 // 🔧 CISCO: Délai de 10 secondes pour synchroniser avec le dégradé de nuit
      });

      // 🌙 CISCO: Phase 1: Apparition TRÈS douce de la lune et du halo (5 secondes)
      // Synchronisée avec le déploiement du dégradé de nuit
      animationRef.current.to(moonRef.current, {
        opacity: 1.0, // 🔧 CISCO: Plus lumineuse (était 0.9)
        duration: 5, // 🔧 CISCO: Plus lent (5s au lieu de 3s) pour synchronisation
        ease: "power2.out"
      });

      // Apparition du halo en parallèle - plus lumineux et plus lent
      animationRef.current.to(haloRef.current, {
        opacity: 0.25, // 🔧 CISCO: Halo plus visible (était 0.15)
        duration: 5, // 🔧 CISCO: Synchronisé avec la lune (5s)
        ease: "power2.out"
      }, 0); // En même temps que la lune

      // 🔧 CISCO: Phase 2: Mouvement en VRAIE courbe parabolique diagonale fluide
      // CORRECTION: Trajectoire diagonale continue du haut-gauche vers bas-droite
      // Durée totale : 360 secondes (6 minutes) pour un mouvement très lent et naturel

      // 🌙 CISCO: Animation de la lune avec trajectoire parabolique FLUIDE
      // Utilisation de motionPath pour une courbe mathématiquement parfaite
      animationRef.current.to(moonRef.current, {
        motionPath: {
          path: "M 15 10 Q 35 5 55 15 Q 75 25 95 70", // Courbe SVG parabolique fluide
          autoRotate: false, // Pas de rotation de la lune
          alignOrigin: [0.5, 0.5] // Centre de la lune comme point d'ancrage
        },
        duration: 360, // 🔧 CISCO: 6 minutes pour mouvement très lent et naturel
        ease: "none", // Vitesse constante pour mouvement astronomique réaliste
        transformOrigin: "center center"
      }, 5); // 🔧 CISCO: Commence après l'apparition complète (5s au lieu de 3s)

      // 🌙 CISCO: Mouvement du halo synchronisé avec la même trajectoire
      animationRef.current.to(haloRef.current, {
        motionPath: {
          path: "M 15 10 Q 35 5 55 15 Q 75 25 95 70", // Même courbe que la lune
          autoRotate: false,
          alignOrigin: [0.5, 0.5]
        },
        duration: 360, // Synchronisé avec la lune
        ease: "none"
      }, 5); // 🔧 CISCO: Synchronisé avec la lune (5s)

    } else if (!isNightMode && currentMode !== 'night') {
      // Arrêter l'animation de descente
      if (animationRef.current) {
        animationRef.current.kill();
        animationRef.current = null;
      }

      // Si la lune est visible, la faire disparaître en douceur avec le halo
      if (moonRef.current && gsap.getProperty(moonRef.current, "opacity") > 0) {
        fadeOutRef.current = gsap.timeline();

        // Disparition de la lune
        fadeOutRef.current.to(moonRef.current, {
          opacity: 0,
          duration: 8,
          ease: "power2.in"
        });

        // Disparition du halo en parallèle
        fadeOutRef.current.to(haloRef.current, {
          opacity: 0,
          duration: 8,
          ease: "power2.in",
          onComplete: () => {
            if (moonRef.current && haloRef.current) {
              gsap.set(moonRef.current, { display: 'none' });
              gsap.set(haloRef.current, { display: 'none' });
            }
          }
        }, 0); // En même temps que la lune
      } else {
        // Si déjà invisible, juste les cacher
        gsap.set(moonRef.current, { display: 'none' });
        gsap.set(haloRef.current, { display: 'none' });
      }
    }

    // Nettoyage au démontage
    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }
    };
  }, [isNightMode, currentMode]);

  return (
    <>
      {/* 🌙 CISCO: Halo lumineux séparé pour éviter l'effet carré */}
      <div
        ref={haloRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 8, // 🔧 CISCO: Lune + Halo derrière les nuages (z-index 8)
          display: 'none',
          width: '200px',
          height: '200px',
          background: 'radial-gradient(circle, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 30%, rgba(255, 255, 255, 0.04) 60%, transparent 100%)', // 🔧 CISCO: Halo plus lumineux
          borderRadius: '50%',
          transform: 'translate(-50%, -50%)',
        }}
      />

      {/* 🌙 CISCO: Lune principale */}
      <div
        ref={moonRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 8, // 🔧 CISCO: Lune + Halo derrière les nuages (z-index 8)
          display: 'none',
          width: '120px',
          height: '120px',
          backgroundImage: 'url(/Lune-Moon.png)',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          filter: 'brightness(1.6) contrast(1.3)', // 🔧 CISCO: Plus lumineuse (était 1.3/1.1)
        }}
        title="🌙 Lune nocturne"
      />
    </>
  );
};

export default MoonAnimation;
